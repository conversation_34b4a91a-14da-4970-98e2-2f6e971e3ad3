/**
 * 留资弹窗组件
 */

'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import { toast } from 'sonner'
import Image from 'next/image'
import { getVerificationCode } from '@/lib/api/image-beautify'

import retentionBg from '/public/images/retentionBg.png'


interface UserInfo {
  name: string
  phone: string
  city: string
  verifyCode: string
}

interface FormErrors {
  name?: string
  phone?: string
  city?: string
  verifyCode?: string
}

interface UserInfoModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (userInfo: UserInfo) => void
  isSubmitting?: boolean
}

export default function UserInfoModal({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false
}: UserInfoModalProps) {
  const { t } = useTranslation()
  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: '',
    phone: '',
    city: '',
    verifyCode: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isCodeSent, setIsCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 表单校验函数
  const validateField = useCallback((field: keyof UserInfo, value: string): string | undefined => {
    switch (field) {
      case 'name':
        if (!value.trim()) return t('retention.errors.nameRequired')
        if (value.length > 20) return t('retention.errors.nameTooLong')
        break
      case 'city':
        if (!value.trim()) return t('retention.errors.cityRequired')
        if (value.length > 10) return t('retention.errors.cityTooLong')
        break
      case 'phone':
        if (!value.trim()) return t('retention.errors.phoneRequired')
        if (!/^1[3-9]\d{9}$/.test(value)) return t('retention.errors.phoneInvalid')
        break
      case 'verifyCode':
        if (!value.trim()) return t('retention.errors.verificationCodeRequired')
        break
    }
    return undefined
  }, [t])



  // 发送验证码
  const handleSendCode = useCallback(async () => {
    const phone = userInfo.phone.trim()

    // 校验手机号并设置错误信息
    const phoneError = validateField('phone', phone)
    if (phoneError) {
      setFormErrors(prev => ({ ...prev, phone: phoneError }))
      return
    }

    // 清除手机号错误
    setFormErrors(prev => ({ ...prev, phone: undefined }))

    try {
      // 调用验证码接口
      const success = await getVerificationCode({ phone })

      if (success) {
        setIsCodeSent(true)
        setCountdown(60)

        // 倒计时
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              setIsCodeSent(false)
              return 0
            }
            return prev - 1
          })
        }, 1000)
      }
    } catch (error) {
      // 网络错误等异常情况仍使用 toast
      toast.error(t('retention.sendCodeFailed', '验证码发送失败'))
    }
  }, [userInfo.phone, validateField])

  // 处理表单提交
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()

    // 校验所有字段
    const errors: FormErrors = {}
    const fields: (keyof UserInfo)[] = ['name', 'city', 'phone', 'verifyCode']

    fields.forEach(field => {
      const error = validateField(field, userInfo[field])
      if (error) {
        errors[field] = error
      }
    })

    // 如果有错误，设置错误状态并阻止提交
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    // 清除所有错误并提交
    setFormErrors({})
    onSubmit(userInfo)
  }, [userInfo, onSubmit, validateField])

  // 处理输入变化
  const handleInputChange = useCallback((field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }))

    // 实时校验并清除错误
    if (formErrors[field]) {
      const error = validateField(field, value)
      if (!error) {
        setFormErrors(prev => ({ ...prev, [field]: undefined }))
      }
    }
  }, [formErrors, validateField])

  // 重置表单
  const handleClose = useCallback(() => {
    setUserInfo({ name: '', phone: '', city: '', verifyCode: '' })
    setFormErrors({})
    setIsCodeSent(false)
    setCountdown(0)
    onClose()
  }, [onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center mt-[89px] lg:mt-[109px] bg-[#ccc] "
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="relative w-[90%] md:w-[860px] bg-white md:h-[600px] max-w-[860px]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 背景图片 */}
            <div className="relative h-full">
              <Image
                src={retentionBg}
                alt="Background"
                fill
                className="object-cover"
                priority
              />

              {/* 内容覆盖层 */}
              <div className="relative z-10 flex h-full px-4 lg:px-0">
                {/* 左侧图片区域 - 在移动端隐藏 */}
                <div className="hidden md:block w-full lg:w-[54%]" />

                {/* 右侧表单区域 */}
                <div className="w-full lg:w-[46%] lg:pr-[55px] flex flex-col  text-white">
                  {/* 标题区域 */}
                  <div className="my-[30px] lg:my-[60px]">
                    <h2 className="text-[22px] lg:text-[30px] font-[300] text-center">
                      {t('retention.title', '免费AI变美')}
                    </h2>
                    <p className="text-[12px] lg:text-[16px] font-[300] text-center">
                      {t('retention.subTitle', '提交后即可下载')}
                    </p>
                  </div>

                  {/* 表单 */}
                  <form onSubmit={handleSubmit} className="space-y-[30px]">
                    {/* 姓名和城市输入 */}
                    <div className=" lg:flex lg:space-x-5">
                      <div className="flex-1 mb-[30px] lg:mb-0 relative">
                        <label className="block text-[12px] font-[300] text-white leading-[16px] mb-3">
                          {t('retention.userName', '姓名')}
                        </label>
                        <input
                          type="text"
                          value={userInfo.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder={t('retention.userNamePlaceholder', '输入姓名')}
                          className={`w-full px-0 py-[2px] bg-transparent border-0 border-b text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none transition-colors ${
                            formErrors.name ? 'border-red-500 focus:border-red-500' : 'border-white/50 focus:border-white/80'
                          }`}
                          disabled={isSubmitting}

                        />
                        {formErrors.name && (
                          <p className="absolute left-0 top-full text-red-400 text-xs mt-1 z-10">{formErrors.name}</p>
                        )}
                      </div>
                      <div className="flex-1 relative">
                        <label className="block text-[12px] font-[300] text-white leading-[16px] mb-3">
                          {t('retention.city', '城市')}
                        </label>
                        <input
                          type="text"
                          value={userInfo.city}
                          onChange={(e) => handleInputChange('city', e.target.value)}
                          placeholder={t('retention.cityPlaceholder', '输入城市')}
                          className={`w-full px-0 py-[2px] bg-transparent border-0 border-b text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none transition-colors ${
                            formErrors.city ? 'border-red-500 focus:border-red-500' : 'border-white/50 focus:border-white/80'
                          }`}
                          disabled={isSubmitting}

                        />
                        {formErrors.city && (
                          <p className="absolute left-0 top-full text-red-400 text-xs mt-1 z-10">{formErrors.city}</p>
                        )}
                      </div>
                    </div>

                    {/* 手机号输入 */}
                    <div className="relative">
                      <label className="block text-[12px] font-[300] text-white leading-[16px] mb-3">
                        {t('retention.phone', '手机号码')}
                      </label>
                      <input
                        type="tel"
                        value={userInfo.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder={t('retention.phonePlaceholder', '输入手机号码')}
                        className={`w-full px-0 py-[2px] bg-transparent border-0 border-b text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none transition-colors ${
                          formErrors.phone ? 'border-red-500 focus:border-red-500' : 'border-white/50 focus:border-white/80'
                        }`}
                        disabled={isSubmitting}

                      />
                      {formErrors.phone && (
                        <p className="absolute left-0 top-full text-red-400 text-xs mt-1 z-10">{formErrors.phone}</p>
                      )}
                    </div>

                    {/* 验证码输入 */}
                    <div className="relative">
                      <label className="block text-[12px] font-[300] text-white leading-[16px] mb-3">
                        {t('retention.verificationCode', '验证码')}
                      </label>
                      <div className={`flex items-center border-b transition-colors ${
                        formErrors.verifyCode ? 'border-red-500 focus-within:border-red-500' : 'border-white/50 focus:border-white/80'
                      }`}>
                        <input
                          type="text"
                          value={userInfo.verifyCode}
                          onChange={(e) => handleInputChange('verifyCode', e.target.value)}
                          placeholder={t('retention.verificationCodePlaceholder', '输入验证码')}
                          className="flex-1 px-0 py-2 bg-transparent border-0 text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none"
                          disabled={isSubmitting}

                        />
                        <div className="w-px h-4 bg-white/30 mx-2"></div>
                        <button
                          type="button"
                          onClick={handleSendCode}
                          disabled={isSubmitting || !userInfo.phone.trim() || isCodeSent}
                          className="w-[40%] text-white text-sm font-[300] transition-colors "
                        >
                          {isCodeSent ? `${countdown}s` : t('retention.sendCode', '获取验证码')}
                        </button>
                      </div>
                      {formErrors.verifyCode && (
                        <p className="absolute left-0 top-full text-red-400 text-xs mt-1 z-10">{formErrors.verifyCode}</p>
                      )}
                    </div>

                    {/* 按钮组 */}
                    <div className="flex space-x-4 pb-[20px] pt-[20px] lg:pt-[50px] lg:pb-[0px]">
                      <button
                        type="button"
                        onClick={handleClose}
                        disabled={isSubmitting}
                        className="flex-1 px-6 py-2 lg:py-3 text-white border border-white text-[16px] lg:text-[18px]"
                      >
                        {t('retention.cancel', '取消')}
                      </button>
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="flex-1 px-6 py-2 lg:py-3 bg-white text-black text-[16px] lg:text-[18px] disabled:opacity-50"
                      >
                        {isSubmitting ? '提交中...' : t('retention.submit', '提交')}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
