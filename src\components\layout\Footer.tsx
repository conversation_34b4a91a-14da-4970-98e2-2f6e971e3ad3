'use client'
import { useState } from 'react'
import { motion } from 'motion/react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslation } from '@/lib/i18n/client'
import type { IFooter, ISite } from '@/types/strapi'

interface FooterProps {
  data: IFooter | null
  siteData: ISite | null
}

export default function Footer({ data, siteData }: FooterProps) {
  const { locale } = useTranslation()
  const [siteAttrData] = useState(siteData || null)
  const [footerData] = useState(data || null)
  const [expandedMenus, setExpandedMenus] = useState<{ [key: number]: boolean }>({})

  const toggleMenu = (menuId: number) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menuId]: !prev[menuId],
    }))
  }

  return (
    <footer className="bg-black text-white">
      <div className="mx-auto w-full pb-4 sm:px-6 lg:w-[76%] lg:px-0">
        {/* PC端布局 */}
        <div className="hidden pt-15 lg:block">
          {/* 上部分：Logo + 标题 + 描述 + 按钮 */}
          <div className="mb-15">
            {siteAttrData?.logo && (
              <Image
                src={siteAttrData.logo.url}
                alt="Lumii Logo"
                width={120}
                height={40}
                className="mb-15 h-8 w-auto"
              />
            )}
            <div className="flex items-start justify-between">
              <div>
                <h2 className="mb-[6px] text-[34px] leading-[48px] font-[300]">
                  {siteAttrData?.title}
                </h2>
                <p className="text-[16px] leading-[22px] font-[300] text-white">
                  {siteAttrData?.slogan}
                </p>
              </div>
              {/* 右侧：免费AI变美按钮 */}
              <div className="flex flex-shrink-0 ml-4 relative top-[10px]">
                {footerData?.buttons &&
                  footerData.buttons.length > 0 &&
                  footerData.buttons.map((button) => (
                    <Link
                      key={button.label}
                      href={`/${locale}/${button.url}`}
                      target="_blank"
                      className="bg-white px-26 py-4 text-[18px] leading-[25px] text-black"
                    >
                      {button.label}
                    </Link>
                  ))}
              </div>
            </div>
          </div>

          {/* 分隔线 */}
          <div className="mb-15 border-t border-[#666]"></div>

          {/* 中间部分：联系中心 + 关于lumii + 社交媒体 */}
          <div className="grid grid-cols-3 pb-[30px]">
            {/* 左侧：联系中心 */}
            <div>
              {data?.menus.find(
                (menu) => menu.label === '联系中心' || menu.label === 'Contact Centre'
              ) && (
                <div>
                  <h4 className="mb-5 text-[16px] font-[500]">
                    {
                      data.menus.find(
                        (menu) => menu.label === '联系中心' || menu.label === 'Contact Centre'
                      )?.label
                    }
                  </h4>
                  <div className="mb-10 text-sm font-[300] text-white">
                    {siteAttrData?.phone && (
                      <p className="leading-[36px]">
                        {locale === 'zh-CN' ? '免费热线：' : 'Phone: '}
                        {siteAttrData.phone}
                      </p>
                    )}
                    {siteAttrData?.email && (
                      <p className="leading-[36px]">
                        {locale === 'zh-CN' ? '邮箱：' : 'Email: '}
                        {siteAttrData.email}
                      </p>
                    )}
                  </div>

                  {/* 二维码 */}
                  {footerData?.qrCodes && footerData.qrCodes.length > 0 && (
                    <div className="flex space-x-6">
                      {footerData.qrCodes.map((qrCode) => (
                        <div key={qrCode.id} className="flex flex-col items-center text-center">
                          {qrCode.image && (
                            <Image
                              src={qrCode.image.url}
                              alt={qrCode.label}
                              width={98}
                              height={98}
                              className="mb-2"
                            />
                          )}
                          <p className="text-sm font-[300] text-white">{qrCode?.label}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 中间：关于lumii */}
            <div className="pl-[20%]">
              {data?.menus.find(
                (menu) => menu.label === '关于lumii' || menu.label === 'About Lumii'
              ) && (
                <div>
                  <h4 className="mb-5 text-[16px] font-[500]">
                    {
                      data.menus.find(
                        (menu) => menu.label === '关于lumii' || menu.label === 'About Lumii'
                      )?.label
                    }
                  </h4>
                  <ul className="space-y-3 text-sm text-gray-400">
                    {data.menus
                      .find((menu) => menu.label === '关于lumii' || menu.label === 'About Lumii')
                      ?.menus?.map(
                        (
                          item: { id?: number; label?: string; url?: string },
                          itemIndex: number
                        ) => (
                          <li key={itemIndex}>
                            <Link
                              href={item.url || '#'}
                              className="transition-colors hover:text-white"
                            >
                              {item.label}
                            </Link>
                          </li>
                        )
                      )}
                  </ul>
                </div>
              )}
            </div>

            {/* 右侧：社交媒体图标 */}
            <div className="flex justify-end">
              {footerData?.socials && footerData.socials.length > 0 && (
                <>
                  {footerData.socials.map((social) => (
                    <Link
                      key={social.id}
                      href={social.url || '#'}
                      className="ml-[10%] transition-opacity hover:opacity-80"
                    >
                      {social.icon && (
                        <Image
                          src={social.icon.url}
                          alt=""
                          width={0}
                          height={0}
                          sizes="100vw"
                          className="felx-shrink-0 h-6 w-auto"
                        />
                      )}
                    </Link>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>

        {/* 移动端布局 */}
        <div className="pt-[30px] lg:hidden">
          {/* Logo + 标题 + 描述 */}
          <div className="mb-[30px] px-4">
            {siteAttrData?.logo && (
              <Image
                src={siteAttrData.logo.url}
                alt="Lumii Logo"
                width={120}
                height={40}
                className="mb-[30px] h-8 w-auto"
              />
            )}
            <h2 className="mb-1 text-[24px] leading-[33px] font-[300]">{siteAttrData?.title}</h2>
            <p className="mb-5 text-sm leading-[20px] font-[300] text-white">
              {siteAttrData?.slogan}
            </p>

            {/* 免费AI变美按钮 */}
            {footerData?.buttons &&
              footerData.buttons.length > 0 &&
              footerData.buttons.map((button) => (
                <Link
                  key={button.label}
                  href={`/${locale}/${button.url}`}
                  className="block w-full bg-white py-4 text-center text-[18px] text-black"
                >
                  {button.label}
                </Link>
              ))}
          </div>

          {/* 分隔线 */}
          <div className="mb-[30px] border-t border-[#666]"></div>
          {/* 可折叠菜单 */}
          {data?.menus.map((menu) => (
            <div key={menu.id} className="mb-[30px] px-4">
              <button
                onClick={() => toggleMenu(menu.id)}
                className="mb-[10px] flex w-full items-center justify-between text-left leading-[22px]"
              >
                <h4 className="text-[16px] font-[500]">{menu.label}</h4>
                {expandedMenus[menu.id] ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {expandedMenus[menu.id] && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  {/* 联系中心内容 */}
                  {(menu.label === '联系中心' || menu.label === 'Contact Centre') && (
                    <div className="text-sm leading-[36px] font-[300] text-white">
                      {siteAttrData?.phone && (
                        <p>
                          {locale === 'zh-CN' ? '免费热线：' : 'Phone: '}
                          {siteAttrData.phone}
                        </p>
                      )}
                      {siteAttrData?.email && (
                        <p>
                          {locale === 'zh-CN' ? '邮箱：' : 'Email: '}
                          {siteAttrData.email}
                        </p>
                      )}
                    </div>
                  )}

                  {/* 关于lumii内容 */}
                  {(menu.label === '关于lumii' || menu.label === 'About Lumii') && (
                    <div className="text-sm leading-[36px] font-[300] text-white">
                      {menu.menus?.map(
                        (
                          item: { id?: number; label?: string; url?: string },
                          itemIndex: number
                        ) => (
                          <div key={itemIndex}>{item.label}</div>
                        )
                      )}
                    </div>
                  )}
                </motion.div>
              )}
            </div>
          ))}

          {/* 移动端社交媒体和二维码 */}
          <div className="mt-6 mb-[30px]">
            {/* 社交媒体图标 */}
            {footerData?.socials && footerData.socials.length > 0 && (
              <div className="mb-[25px] flex justify-center space-x-10">
                {footerData.socials.map((social) => (
                  <Link
                    key={social.id}
                    href={social.url || '#'}
                    className="transition-opacity hover:opacity-80"
                  >
                    {social.icon && (
                      <Image
                        src={social.icon.url}
                        alt=""
                        width={0}
                        height={0}
                        sizes="100vw"
                        className="h-6 w-auto"
                      />
                    )}
                  </Link>
                ))}
              </div>
            )}

            {/* 二维码 */}
            {footerData?.qrCodes && footerData.qrCodes.length > 0 && (
              <div className="flex justify-center space-x-6">
                {footerData.qrCodes.map((qrCode) => (
                  <div key={qrCode.id} className="flex flex-col items-center text-center">
                    {qrCode.image && (
                      <Image
                        src={qrCode.image.url}
                        alt={qrCode.label}
                        width={98}
                        height={98}
                        className="mb-2"
                      />
                    )}
                    <p className="text-sm leading-[28px] font-[300] text-white">{qrCode?.label}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-[#666] pt-4 text-center px-4">
          <p className="legading-[36px] text-sm font-[300] text-white">{siteAttrData?.legal}</p>
        </div>
      </div>
    </footer>
  )
}
