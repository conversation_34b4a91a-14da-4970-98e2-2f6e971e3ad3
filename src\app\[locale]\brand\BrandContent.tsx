'use client'

/**
 * 品牌页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'
import { sub } from 'motion/react-client'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface BrandContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 优化的动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.05,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 15 },
  animate: { opacity: 1, y: 0 },
}

const scaleOnHover = {
  whileHover: { scale: 1.01 },
  transition: { duration: 0.2 },
}

// 区块组件
const BlockRenderer = ({
  block,
  index,
}: {
  block: StrapiBlock & { media?: Media; buttons?: Button[] }
  index: number
}) => {
  const { title, subtitle, description, media } = block

  if (index === 0) {
    return (
      <motion.section
        className="px-4 py-10 lg:px-0 lg:py-20"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <motion.div
          className="flex flex-col"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* 上部：图片 */}
          <motion.div className="mb-5 lg:mb-10" variants={fadeInUp} transition={{ duration: 0.3, ease: 'easeOut' }}>
            {media && (
              <motion.div className="relative" {...scaleOnHover}>
                <Image
                  src={media.url}
                  alt={title || ''}
                  width={1200}
                  height={0}
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
          </motion.div>

          {/* 中部：标题 */}
          <motion.div
            className="text-center"
            variants={fadeInUp}
            transition={{ duration: 0.3, delay: 0.1, ease: 'easeOut' }}
          >
            <h2 className="mb-2 text-[18px] leading-[36px] font-[500] lg:text-[22px]">{title}</h2>
          </motion.div>

          {/* 下部：描述文字 */}
          <motion.p
            className="line-clamp-responsive text-justify text-sm leading-[20px] lg:leading-[28px] font-[300] text-[#444444]"
            variants={fadeInUp}
            transition={{ duration: 0.3, delay: 0.15, ease: 'easeOut' }}
          >
            {description}
          </motion.p>
        </motion.div>
      </motion.section>
    )
  }

  if (index === 1) {
    return (
      <motion.section
        className="px-4 py-10 lg:px-0 lg:py-20"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <motion.div
          className="flex flex-col lg:flex-row lg:justify-center lg:gap-20"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* 移动端：标题 */}
          <motion.div className="lg:hidden" variants={fadeInUp}>
            <h2 className="mb-3 text-[18px] leading-[30px] font-[500]">{title}</h2>
          </motion.div>

          {/* 左侧图片 */}
          <motion.div className="order-2 lg:order-1 lg:flex-1" variants={fadeInUp}>
            {media && (
              <motion.div {...scaleOnHover}>
                <Image
                  src={media.url}
                  alt={title || ''}
                  width={500}
                  height={320}
                  className="h-auto w-full object-cover lg:h-auto lg:w-full"
                />
              </motion.div>
            )}
          </motion.div>

          {/* 右侧内容 */}
          <motion.div
            className="order-1 flex  flex-col lg:order-2 lg:flex-1 lg:justify-center"
            variants={fadeInUp}
          >
            <div>
              {/* PC端：标题 */}
              <h2 className="mb-3 hidden text-[28px] leading-[40px] font-[500] lg:block">
                {title}
              </h2>

              {/* 职务 */}
              {subtitle && (
                <div className=" mb-3 lg:mb-22">
                  {subtitle.split(/[;、,]/).filter(item => item.trim()).map((item, index) => (
                    <p key={index} className="text-sm leading-[22px]  lg:leading-[28px] font-[500]">
                      {item.trim()}
                    </p>
                  ))}
                </div>
              )}

              {/* 描述文字 */}
              <div className="line-clamp-responsive mb-[30px] text-sm leading-[20px] lg:leading-[28px] font-[300] text-[#444444] lg:mb-0">
                {description}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.section>
    )
  }
  return null
}

// 主要的 BrandContent 组件
export default function BrandContent({ pageData }: BrandContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-24">
        <p>页面数据加载中...</p>
      </div>
    )
  }

  return (
    <div className="bg-[#000] pt-[86px] lg:pt-[102px]">
      {/* Hero Banner */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="relative"
            initial={{ scale: 1.02 }}
            animate={{ scale: 1 }}
            transition={{ duration: 1.2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-10 left-7.5 z-10 w-full text-[#fff] lg:top-[30%] lg:left-[12%]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm leading-[20px] lg:leading-[28px] font-[300] lg:block lg:text-justify"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="bg-[#FAF6F2]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="lg:mx-auto lg:w-[76%]">
          {pageData.blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05, ease: 'easeOut' }}
                viewport={{ once: true, amount: 0.05 }}
              >
                <BlockRenderer block={block} index={index} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
