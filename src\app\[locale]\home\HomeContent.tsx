'use client'

/**
 * 首页内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import Link from 'next/link'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface HomeContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 优化的动画配置
const fadeInUp = {
  initial: { opacity: 0, y: 15 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.4, ease: 'easeOut' },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.05,
    },
  },
}

const scaleOnHover = {
  whileHover: { scale: 1.02 },
  transition: { duration: 0.2 },
}

// 区块组件
const BlockRenderer = ({
  block,
  index,
}: {
  block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] }
  index: number
}) => {
  const { title, description } = block
  const { media, buttons } = block as StrapiBlock & {
    media?: Media
    buttons?: Button[]
  }

  // 根据标题判断是品牌介绍还是产品介绍
  if (index === 0) {
    return (
      <motion.section
        className="py-10 lg:py-20"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <div className="container mx-auto px-4 lg:px-0">
          <motion.div
            className="flex flex-col lg:flex-row lg:items-stretch lg:gap-20"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.1 }}
          >
            {/* 移动端：标题 */}
            <motion.div className="lg:hidden" variants={fadeInUp}>
              <h2 className="mb-3 text-[20px]">{title}</h2>
            </motion.div>

            {/* 左侧图片 */}
            <motion.div
              className="order-2 lg:order-1 lg:flex lg:flex-1 lg:flex-col"
              variants={fadeInUp}
            >
              {media && (
                <motion.div className="lg:flex lg:flex-1 lg:items-stretch" {...scaleOnHover}>
                  <Image
                    src={media.url}
                    alt={title || ''}
                    width={500}
                    height={320}
                    className="h-auto w-full object-cover lg:h-full lg:w-full lg:object-cover"
                  />
                </motion.div>
              )}
            </motion.div>

            {/* 右侧内容 */}
            <motion.div
              className="order-1 flex flex-col lg:order-2 lg:min-h-[300px] lg:flex-1 lg:justify-between"
              variants={fadeInUp}
            >
              <div>
                {/* PC端：标题 */}
                <h2 className="mb-8 hidden text-4xl font-[30px] lg:block">{title}</h2>

                {/* 描述文字 */}
                <div className="line-clamp-responsive text-justify text-sm leading-[20px] font-[300] text-[#444444] lg:leading-[28px]">
                  {description}
                </div>
              </div>

              {/* 按钮区域 */}
              {buttons && buttons.length > 0 && (
                <motion.div
                  className="mt-4 mb-7 flex gap-4 lg:mt-0 lg:mb-0"
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                >
                  {buttons.map((button: Button, idx: number) => (
                    <Link
                      href={button.url || '#'}
                      key={idx}
                      className="bg-black px-11 py-[6px] text-sm leading-[20px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3 lg:leading-[16px]"
                    >
                      {button.label}
                    </Link>
                  ))}
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    )
  }

  // 产品介绍
  if (index === 1) {
    return (
      <motion.section
        className="py-10 lg:py-20"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <div className="container mx-auto px-4 lg:px-0">
          <motion.div
            className="flex flex-col lg:flex-row lg:items-stretch lg:gap-20"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.1 }}
          >
            {/* 移动端：标题 */}
            <motion.div className="lg:hidden" variants={fadeInUp}>
              <h2 className="mb-3 text-[20px]">{title}</h2>
            </motion.div>

            {/* 左侧内容 */}
            <motion.div
              className="flex flex-col lg:min-h-[300px] lg:flex-1 lg:justify-between"
              variants={fadeInUp}
            >
              <div>
                {/* PC端：标题 */}
                <h2 className="mb-8 hidden text-4xl font-[30px] lg:block">{title}</h2>

                {/* 描述文字 */}
                <div className="line-clamp-responsive text-justify text-sm leading-[20px] font-[300] text-[#444444] lg:leading-[28px]">
                  {description}
                </div>
              </div>

              {/* 按钮区域 */}
              {buttons && buttons.length > 0 && (
                <motion.div
                  className="mt-4 mb-7 flex gap-4 lg:mt-0 lg:mb-0"
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                >
                  {buttons.map((button: Button, idx: number) => (
                    <Link
                      href={button.url || '#'}
                      key={idx}
                      className="bg-black px-11 py-[6px] text-sm leading-[20px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3 lg:leading-[16px]"
                    >
                      {button.label}
                    </Link>
                  ))}
                </motion.div>
              )}
            </motion.div>

            {/* 右侧图片 */}
            <motion.div
              className="flex justify-center lg:flex lg:flex-1 lg:flex-col lg:justify-end"
              variants={fadeInUp}
            >
              {media && (
                <motion.div className="lg:flex lg:flex-1 lg:items-stretch" {...scaleOnHover}>
                  <Image
                    src={media.url}
                    alt={title || ''}
                    width={500}
                    height={320}
                    className="h-auto w-full object-cover lg:h-full lg:w-full lg:object-cover"
                  />
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    )
  }
  // 用户案例
  if (index === 2) {
    return (
      <motion.section
        className="py-10 lg:py-20"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <div className="container mx-auto px-4 lg:px-0">
          <motion.div
            className="flex h-full flex-col justify-between"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.1 }}
          >
            <div>
              <motion.h2
                className="mb-10 text-center text-[20px] font-[30px] lg:mb-15 lg:text-4xl"
                variants={fadeInUp}
              >
                {title}
              </motion.h2>

              {/* 用户案例网格 */}
              <motion.div
                className="mb-10 flex flex-col space-y-[30px] lg:flex-row lg:justify-between lg:gap-10 lg:space-y-0"
                variants={staggerContainer}
              >
                {block.blocks?.map((caseBlock: StrapiBlock & { media?: Media }, idx: number) => (
                  <motion.div
                    key={idx}
                    className="group relative lg:flex-1"
                    variants={fadeInUp}
                    whileHover={{ y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="relative overflow-hidden">
                      {caseBlock.media?.url ? (
                        <>
                          <motion.div {...scaleOnHover}>
                            <Image
                              src={caseBlock.media.url}
                              alt={caseBlock.title || `Case ${idx + 1}`}
                              width={340}
                              height={420}
                              className="h-auto w-full object-cover transition-transform duration-300 lg:group-hover:scale-105"
                            />
                          </motion.div>

                          {/* 移动端：直接显示的模态框 */}
                          <motion.div
                            className="absolute right-0 bottom-0 left-0 bg-black/80 px-6 pt-5 pb-7 text-white lg:hidden"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                          >
                            <h3 className="mb-4 text-[20px] leading-[28px] font-[600]">
                              {caseBlock.title}
                            </h3>
                            <p className="text-sm leading-[22px]">{caseBlock.description}</p>
                          </motion.div>

                          {/* PC端：鼠标悬停时显示的模态框 */}
                          <div className="absolute right-0 bottom-0 left-0 hidden translate-y-full transform bg-black/80 px-6 pt-5 pb-7 text-white transition-transform duration-300 ease-in-out group-hover:translate-y-0 lg:block">
                            <h3 className="mb-4 text-[20px] leading-[28px] font-[600]">
                              {caseBlock.title}
                            </h3>
                            <p className="text-sm leading-[22px]">{caseBlock.description}</p>
                          </div>
                        </>
                      ) : (
                        // 如果没有图片，显示占位符
                        <motion.div
                          className="flex h-auto w-full items-center justify-center bg-gray-200 lg:h-[420px]"
                          variants={fadeInUp}
                        >
                          <div className="p-6 text-center">
                            <h3 className="mb-2 text-xl font-semibold text-gray-800">
                              {caseBlock.title}
                            </h3>
                            <p className="text-sm text-gray-600">{caseBlock.description}</p>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>

            {/* 按钮区域 */}
            {buttons && buttons.length > 0 && (
              <motion.div className="text-center" variants={fadeInUp}>
                <div className="flex justify-center gap-4">
                  {buttons.map((button: Button, idx: number) => (
                    <Link
                      href={button.url || '#'}
                      key={idx}
                      className="bg-black px-11 py-[6px] text-sm leading-[20px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3 lg:leading-[16px]"
                    >
                      {button.label}
                    </Link>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </motion.section>
    )
  }
  // 授权医生
  if (index === 3) {
    return (
      <section className="py-10 lg:py-20">
        <div className="container mx-auto px-4 lg:px-0">
          <div className="flex h-full flex-col justify-between">
            <div>
              <h2 className="mb-10 text-center text-[20px] font-[30px] lg:mb-15 lg:text-4xl">
                {title}
              </h2>

              {/* 医生网格 */}
              <div className="grid grid-cols-2 gap-x-[15px] px-[9px] lg:grid-cols-4">
                {block.blocks?.map(
                  (
                    doctorBlock: StrapiBlock & { media?: Media; subtitle?: string | null },
                    idx: number
                  ) => (
                    <div key={idx} className="mb-10">
                      <div className="relative mb-5 overflow-hidden">
                        {doctorBlock.media?.url ? (
                          <Image
                            src={doctorBlock.media.url}
                            alt={doctorBlock.title || `Doctor ${idx + 1}`}
                            width={200}
                            height={250}
                            className="h-auto w-full object-cover transition-transform duration-300 ease-in-out hover:scale-110"
                          />
                        ) : (
                          // 如果没有图片，显示占位符
                          <div className="flex h-64 w-full items-center justify-center bg-gray-200 transition-transform duration-300 ease-in-out hover:scale-110">
                            <div className="text-center">
                              <div className="mx-auto mb-2 h-16 w-16 rounded-full bg-gray-400"></div>
                              <p className="text-sm text-gray-500">医生照片</p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* 医生信息 */}
                      <h3 className="mb-2 text-lg leading-[25px] font-[500]">
                        {doctorBlock.title}
                      </h3>
                      <p className="text-sm leading-[20px] font-[300] text-[#444444]">
                        {doctorBlock.subtitle || doctorBlock.description}
                      </p>
                    </div>
                  )
                )}
              </div>
            </div>

            {/* 按钮区域 */}
            {buttons && buttons.length > 0 && (
              <div className="text-center">
                <div className="flex justify-center gap-4">
                  {buttons.map((button: Button, idx: number) => (
                    <Link
                      href={button.url || '#'}
                      key={idx}
                      className="lg:py-3leading-[20px] bg-black px-11 py-[6px] text-sm text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3 lg:leading-[16px]"
                    >
                      {button.label}
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    )
  }

  // 授权诊所
  if (index === 4) {
    return (
      <motion.section
        className="py-10 lg:py-20"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
        viewport={{ once: true, amount: 0.1 }}
      >
        <div className="container mx-auto px-4 lg:px-0">
          <motion.div
            className="flex h-full flex-col justify-between"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true, amount: 0.1 }}
          >
            <div>
              <motion.h2
                className="mb-10 text-center text-[20px] font-[30px] lg:mb-15 lg:text-4xl"
                variants={fadeInUp}
              >
                {title}
              </motion.h2>

              {/* 诊所大图 */}
              <motion.div className="mb-8" variants={fadeInUp}>
                {media && (
                  <motion.div {...scaleOnHover}>
                    <Image
                      src={media.url}
                      alt={title || ''}
                      width={1200}
                      height={600}
                      className="h-auto w-full object-contain lg:h-96 lg:object-cover"
                    />
                  </motion.div>
                )}
              </motion.div>

              {/* 描述文字 */}
              {description && (
                <motion.div className="mb-10" variants={fadeInUp}>
                  <div className="line-clamp-3 text-sm leading-[20px] font-[300] text-[#444444] lg:line-clamp-none lg:leading-[28px]">
                    {description}
                  </div>
                </motion.div>
              )}
            </div>

            {/* 按钮区域 */}
            {buttons && buttons.length > 0 && (
              <motion.div className="text-center" variants={fadeInUp}>
                <div className="flex justify-center gap-4">
                  {buttons.map((button: Button, idx: number) => (
                    <Link
                      href={button.url || '#'}
                      key={idx}
                      className="bg-black px-11 py-[6px] text-sm leading-[20px] text-white transition-colors hover:bg-gray-800 lg:px-12 lg:py-3 lg:leading-[16px]"
                    >
                      {button.label}
                    </Link>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </motion.section>
    )
  }

  return null
}

// 主要的 HomeContent 组件
export default function HomeContent({ pageData }: HomeContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }

  return (
    <div className="pt-[86px] lg:pt-0">
      {/* Hero Banner */}
      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map(
          (item: StrapiHero) =>
            item.media && (
              <motion.video
                key={item.id}
                src={item.media.url}
                className="h-auto w-full object-contain lg:h-screen lg:object-cover"
                autoPlay
                muted
                loop
                initial={{ scale: 1.1 }}
                animate={{ scale: 1 }}
                transition={{ duration: 2, ease: 'easeOut' }}
              />
            )
        )}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="bg-[#FAF6F2]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="md:mx-auto md:w-[76%] md:max-w-none">
          {pageData.blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05, ease: 'easeOut' }}
                viewport={{ once: true, amount: 0.05 }}
              >
                <BlockRenderer block={block} index={index} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
